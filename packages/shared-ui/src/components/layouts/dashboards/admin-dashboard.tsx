import { ReactNode, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Settings,
  Users,
  Database,
  Shield,
  Activity,
  Bell,
  Search,
  Menu,
  ChevronDown,
  Home,
  FileText,
  BarChart3,
  Zap,
  HelpCircle,
  LogOut
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput } from '../../ui/forms';
import { LuminarBadge, LuminarAvatar } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import { createGlassStyles } from "../../../lib/glass-utils";
import { animationPresets, transitions } from "../../../design-system";
import { microInteractions, createStaggeredList } from "../../../lib/micro-interactions";
import type {
  StandardComponentProps,
  ComponentVariant,
  defaultComponentProps
} from '../../../types/component-props';

export interface AdminDashboardProps {
  className?: string;
  user?: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
  };
  navigation?: NavigationItem[];
  children: ReactNode;
  headerActions?: ReactNode;
  notifications?: Notification[];
  quickActions?: QuickAction[];
  showBreadcrumbs?: boolean;
  breadcrumbs?: Breadcrumb[];
  sidebarCollapsed?: boolean;
  onSidebarToggle?: (collapsed: boolean) => void;
}

export interface NavigationItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  href?: string;
  onClick?: () => void;
  active?: boolean;
  badge?: string | number;
  children?: NavigationItem[];
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  timestamp: Date;
  read?: boolean;
}

export interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  variant?: ComponentVariant | 'destructive' | 'outline';
}

export interface Breadcrumb {
  label: string;
  href?: string;
  onClick?: () => void;
}

const defaultNavigation: NavigationItem[] = [
  { id: 'dashboard', label: 'Dashboard', icon: Home, active: true },
  { id: 'users', label: 'Users', icon: Users, badge: '12' },
  { id: 'content', label: 'Content', icon: FileText },
  { id: 'analytics', label: 'Analytics', icon: BarChart3 },
  { id: 'settings', label: 'Settings', icon: Settings },
  { id: 'security', label: 'Security', icon: Shield },
  { id: 'database', label: 'Database', icon: Database },
  { id: 'activity', label: 'Activity', icon: Activity },
];

const defaultQuickActions: QuickAction[] = [
  { id: 'new-user', label: 'Add User', icon: Users, onClick: () => {} },
  { id: 'backup', label: 'Backup', icon: Database, onClick: () => {} },
  { id: 'maintenance', label: 'Maintenance', icon: Settings, onClick: () => {} },
];

export function AdminDashboard({
  className,
  user = {
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'Administrator'
  },
  navigation = defaultNavigation,
  children,
  headerActions,
  notifications = [],
  quickActions = defaultQuickActions,
  showBreadcrumbs = true,
  breadcrumbs = [],
  sidebarCollapsed: controlledCollapsed,
  onSidebarToggle,
  ...props
}: AdminDashboardProps) {
  const [internalCollapsed, setInternalCollapsed] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const collapsed = controlledCollapsed ?? internalCollapsed;
  const setSidebarCollapsed = onSidebarToggle ?? setInternalCollapsed;

  const unreadCount = notifications.filter(n => !n.read).length;

  const sidebarGlassStyles = createGlassStyles({
    element: 'sidebar',
    profile: 'hard',
    interactive: false,
    frost: true,
    glow: false
  });

  const staggeredNav = createStaggeredList(navigation.length, 0.1);

  return (
    <div className={cn(
      "min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50",
      "dark:from-gray-900 dark:via-slate-900 dark:to-indigo-950 flex",
      className
    )} {...props}>
      
      {/* Sidebar */}
      <motion.aside
        className={cn(
          sidebarGlassStyles,
          "flex flex-col border-r border-white/10 dark:border-white/5",
          "shadow-2xl shadow-black/10 backdrop-blur-xl",
          collapsed ? "w-16" : "w-64"
        )}
        initial={{ x: -100, opacity: 0 }}
        animate={{
          x: 0,
          opacity: 1,
          width: collapsed ? 64 : 256
        }}
        transition={{
          type: "spring" as const,
          stiffness: 300,
          damping: 30,
          opacity: { duration: 0.3 },
          x: { duration: 0.4 }
        }}
      >
        {/* Sidebar Header */}
        <motion.div
          className="p-4 border-b border-white/10 dark:border-white/5"
          whileHover={{
            scale: 1.01,
            transition: { duration: 0.3, ease: "easeOut" as const }
          }}
        >
          <div className="flex items-center justify-between">
            <AnimatePresence mode="wait">
              {!collapsed && (
                <motion.h2
                  key="title"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ type: "spring" as const, stiffness: 300, damping: 30 }}
                  className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent"
                >
                  Admin Panel
                </motion.h2>
              )}
            </AnimatePresence>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarCollapsed(!collapsed)}
                className={cn(
                  createGlassStyles({ element: 'button', profile: 'soft' }),
                  "hover:bg-white/20 dark:hover:bg-white/10"
                )}
              >
                <motion.div
                  animate={{ rotate: collapsed ? 0 : 180 }}
                  transition={{ type: "spring" as const, stiffness: 300, damping: 30 }}
                >
                  <Menu className="w-4 h-4" />
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </motion.div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          <motion.div
            variants={staggeredNav.container}
            initial="initial"
            animate="animate"
            className="space-y-2"
          >
            {navigation.map((item, index) => (
              <motion.div
                key={item.id}
                variants={staggeredNav.item}
                whileHover={{ x: 4 }}
                transition={{ duration: 0.15, ease: "easeInOut" as const }}
              >
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant={item.active ? "default" : "ghost"}
                    className={cn(
                      "w-full gap-3 relative overflow-hidden",
                      collapsed ? "justify-center px-2" : "justify-start",
                      item.active && createGlassStyles({
                        element: 'button',
                        profile: 'standard',
                        interactive: true,
                        glow: true
                      }),
                      !item.active && "hover:bg-white/10 dark:hover:bg-white/5"
                    )}
                    onClick={item.onClick}
                  >
                    {/* Glowing background for active item */}
                    {item.active && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20"
                        animate={{
                          opacity: [0.3, 0.6, 0.3],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                    )}
                    
                    {item.icon && (
                      <motion.div
                        animate={{
                          rotate: item.active ? [0, 5, -5, 0] : 0,
                        }}
                        transition={{
                          duration: 0.5,
                          repeat: item.active ? Infinity : 0,
                          repeatDelay: 3
                        }}
                      >
                        <item.icon className="w-4 h-4 relative z-10" />
                      </motion.div>
                    )}
                    
                    <AnimatePresence mode="wait">
                      {!collapsed && (
                        <motion.div
                          initial={{ opacity: 0, width: 0 }}
                          animate={{ opacity: 1, width: "auto" }}
                          exit={{ opacity: 0, width: 0 }}
                          transition={{ duration: 0.15, ease: "easeInOut" as const }}
                          className="flex items-center justify-between flex-1"
                        >
                          <span className="text-left relative z-10">{item.label}</span>
                          {item.badge && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{
                                delay: 0.2,
                                type: "spring" as const,
                                stiffness: 400,
                                damping: 10
                              }}
                            >
                              <LuminarBadge
                                variant="info"
                                size="sm"
                                className="relative z-10"
                              >
                                {item.badge}
                              </LuminarBadge>
                            </motion.div>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Button>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </nav>

        {/* Sidebar Footer */}
        <motion.div
          className="p-4 border-t border-white/10 dark:border-white/5"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: 0.5,
            type: "spring" as const,
            stiffness: 300,
            damping: 30
          }}
        >
          <AnimatePresence mode="wait">
            {!collapsed ? (
              <motion.div
                key="expanded-footer"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-y-2"
              >
                <motion.div whileHover={{ x: 4 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3 hover:bg-white/10 dark:hover:bg-white/5"
                  >
                    <HelpCircle className="w-4 h-4" />
                    Help & Support
                  </Button>
                </motion.div>
                <motion.div whileHover={{ x: 4 }} whileTap={{ scale: 0.98 }}>
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3 hover:bg-white/10 dark:hover:bg-white/5 text-red-400 hover:text-red-300"
                  >
                    <LogOut className="w-4 h-4" />
                    Sign Out
                  </Button>
                </motion.div>
              </motion.div>
            ) : (
              <motion.div
                key="collapsed-footer"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-y-2"
              >
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                  <Button variant="ghost" size="sm" className="w-full justify-center">
                    <HelpCircle className="w-4 h-4" />
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                  <Button variant="ghost" size="sm" className="w-full justify-center text-red-400">
                    <LogOut className="w-4 h-4" />
                  </Button>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Header */}
        <motion.header
          className={cn(
            createGlassStyles({
              element: 'card',
              profile: 'standard',
              interactive: false,
              frost: true
            }),
            "border-b border-white/10 dark:border-white/5 px-6 py-4",
            "backdrop-blur-xl shadow-lg"
          )}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: 0.2,
            type: "spring" as const,
            stiffness: 300,
            damping: 30
          }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {showBreadcrumbs && breadcrumbs.length > 0 && (
                <nav className="flex items-center gap-2 text-sm">
                  {breadcrumbs.map((crumb, index) => (
                    <div key={index} className="flex items-center gap-2">
                      {index > 0 && <span className="text-gray-400">/</span>}
                      <button
                        onClick={crumb.onClick}
                        className={cn(
                          "hover:text-blue-600 dark:hover:text-blue-400",
                          index === breadcrumbs.length - 1
                            ? "text-gray-900 dark:text-white font-medium"
                            : "text-gray-600 dark:text-gray-400"
                        )}
                      >
                        {crumb.label}
                      </button>
                    </div>
                  ))}
                </nav>
              )}
            </div>

            <div className="flex items-center gap-4">
              {/* Search */}
              <LuminarInput
                placeholder="Search..."
                className="w-64"
                size="sm"
                icon={Search}
              />

              {/* Quick Actions */}
              {quickActions.length > 0 && (
                <div className="flex items-center gap-2">
                  {quickActions.slice(0, 3).map((action) => (
                    <Button
                      key={action.id}
                      variant={action.variant || "outline"}
                      size="sm"
                      onClick={action.onClick}
                    >
                      <action.icon className="w-4 h-4 mr-2" />
                      {action.label}
                    </Button>
                  ))}
                </div>
              )}

              {/* Notifications */}
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNotifications(!showNotifications)}
                >
                  <Bell className="w-4 h-4" />
                  {unreadCount > 0 && (
                    <LuminarBadge
                      variant="error"
                      size="sm"
                      className="absolute -top-1 -right-1 min-w-[1.25rem] h-5"
                    >
                      {unreadCount}
                    </LuminarBadge>
                  )}
                </Button>

                <AnimatePresence>
                  {showNotifications && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95, rotateX: -15 }}
                      animate={{ opacity: 1, y: 0, scale: 1, rotateX: 0 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95, rotateX: 15 }}
                      transition={{ type: "spring" as const, stiffness: 300, damping: 30 }}
                      className="absolute right-0 top-full mt-2 w-80 z-50"
                    >
                      <LuminarCard
                        className="p-4 max-h-96 overflow-y-auto"
                        glassConfig={{
                          element: 'modal',
                          profile: 'intense',
                          interactive: false,
                          frost: true,
                          glow: true
                        }}
                      >
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="font-semibold">Notifications</h3>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowNotifications(false)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        {notifications.length === 0 ? (
                          <p className="text-gray-500 text-center py-4">
                            No notifications
                          </p>
                        ) : (
                          <div className="space-y-3">
                            {notifications.slice(0, 5).map((notification) => (
                              <div
                                key={notification.id}
                                className={cn(
                                  "p-3 rounded-lg border",
                                  notification.read
                                    ? "bg-gray-50 dark:bg-gray-800"
                                    : "bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800"
                                )}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <h4 className="font-medium text-sm">
                                      {notification.title}
                                    </h4>
                                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                      {notification.message}
                                    </p>
                                  </div>
                                  <LuminarBadge
                                    variant={notification.type}
                                    size="sm"
                                  >
                                    {notification.type}
                                  </LuminarBadge>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </LuminarCard>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* User Menu */}
              <div className="relative">
                <Button
                  variant="ghost"
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center gap-2"
                >
                  <LuminarAvatar
                    src={user.avatar}
                    alt={user.name}
                    size="sm"
                    fallback={user.name.slice(0, 2).toUpperCase()}
                  />
                  <div className="text-left hidden md:block">
                    <p className="text-sm font-medium">{user.name}</p>
                    <p className="text-xs text-gray-500">{user.role}</p>
                  </div>
                  <ChevronDown className="w-4 h-4" />
                </Button>

                <AnimatePresence>
                  {showUserMenu && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95, rotateX: -15 }}
                      animate={{ opacity: 1, y: 0, scale: 1, rotateX: 0 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95, rotateX: 15 }}
                      transition={{ type: "spring" as const, stiffness: 300, damping: 30 }}
                      className="absolute right-0 top-full mt-2 w-64 z-50"
                    >
                      <LuminarCard
                        className="p-4"
                        glassConfig={{
                          element: 'modal',
                          profile: 'intense',
                          interactive: false,
                          frost: true,
                          glow: true
                        }}
                      >
                        <div className="space-y-3">
                          <div className="pb-3 border-b border-gray-200 dark:border-gray-700">
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-gray-500">{user.email}</p>
                          </div>
                          
                          <Button variant="ghost" className="w-full justify-start gap-3">
                            <Settings className="w-4 h-4" />
                            Account Settings
                          </Button>
                          
                          <Button variant="ghost" className="w-full justify-start gap-3">
                            <Shield className="w-4 h-4" />
                          </Button>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Dark Mode</span>
                            <ThemeToggle />
                          </div>
                          
                          <hr className="border-gray-200 dark:border-gray-700" />
                          
                          <Button variant="ghost" className="w-full justify-start gap-3">
                            <LogOut className="w-4 h-4" />
                            Sign Out
                          </Button>
                        </div>
                      </LuminarCard>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {headerActions}
            </div>
          </div>
        </motion.header>

        {/* Main Content Area */}
        <motion.main
          className="flex-1 p-6 overflow-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: 0.3,
            type: "spring" as const,
            stiffness: 300,
            damping: 30
          }}
        >
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            {children}
          </motion.div>
        </motion.main>
      </div>
    </div>
  );
}